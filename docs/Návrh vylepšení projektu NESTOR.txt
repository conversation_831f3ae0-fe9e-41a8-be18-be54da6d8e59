Návrh vylepšení projektu NESTOR
Projekt NESTOR počítá s architekturou <PERSON> (FastAPI), PostgreSQL+pgvector, RAG, LLM na
pozadí, frontendem ve Vue.js, LoRA tréninkem a monitorovacími nás<PERSON>ji (Prometheus, Grafana) apod.
Po analýze dokumentů README a IMPLEMENTACE byly zjištěny některé nedostatky a opomenuté
oblasti, které je vhodné před vývojem doplnit a navrhnout osvědčená řešení. Následující kapitoly shrnují
hlavní slabiny a doporučení v oblastech bezpečnosti, škálovatelnosti, správy tajemství, CI/CD,
testování, observability a udržitelnosti systému. Pro přehlednost jsou doporučené přístupy a
nástroje uvedeny v bodech a zejména v přehledné tabulce na konci.
Klíčové oblasti k doplnění (slabiny současného návrhu)
      • Bezpečnost: zatím chybí podrobný plán autentizace/autorizace, ochrana kontejnerů a sítí,
        šifrování dat, pravidla RBAC (role-based access) a zabezpečení RAG komponent (vektorizovaná
        data). Nejsou definovány procesy pro skenování zranitelností ani ochranu před útoky (např. rate
        limiting, WAF).
      • Správa tajemství (secrets): není popsáno, jak budou uchovávána citlivá data (API klíče,
        certifikáty) – řešení jako Vault nebo Kubernetes Secrets zde chybí.
      • CI/CD a DevOps: není dostatečně specifikováno, jak budou automaticky sestavovány, testovány
        a nasazovány kontejnerizované služby (pipeline pro code build, testy, rollout) a jak se budou řídit
        více prostředí (dev/staging/prod).
      • Testování: dokumentace zmiňuje trénink modelů a API, ale neřeší strategii jednotkových,
        integračních, kontraktních a end-to-end testů. Zvláště u mikroslužeb je potřeba zajistit
        automatizované testy při každé změně kódu.
      • Škálovatelnost a výkon: není detailně rozpracován autoscaling (horizontální, vertikální),
        caching, load balancování ani způsoby, jak otestovat výkonnost (zátěžové testy). Potřebné je
        plánování kapacity, asynchronní zprávy apod.
      • Observabilita (monitoring): plán zahrnuje Prometheus a Grafana pro metriky, ale chybí popis
        sběru logů a tracingu. Doporučuje se doplnit centralizované logování, distribuované trasování a
        definování alertů/SLO.
      • Udržitelnost a rozšiřitelnost: není zmíněno pravidlo verzování API, dokumentace (OpenAPI),
        kodexy (linty, review), Infrastructure-as-Code, správa závislostí, či plán obnovy po havárii (zálohy
        DB).
Bezpečnost (Security)
      • Vícevrstvá ochrana: Vytvořit obranné vrstvy napříč sítí, službami a daty. Používat firewally, VPN
        tunely či service mesh (mTLS) pro zabezpečení interní komunikace. Nasadit API Gateway (např.
        Kong, Apigee) s TLS terminací a řízením přístupu, rate limiting a autentizací. V rámci mikroservis
        dodržovat princip nejmenších práv – každá služba má pouze oprávnění, které skutečně
        potřebuje.
      • Šifrování a síťová bezpečnost: Vynutit SSL/TLS pro všechna API rozhraní i přístup k databázím.
        Aktivovat šifrování dat v klidu (diskové svazky, šifrované volume) a zálohovacích souborech.
        Zajistit oddělení provozu (např. separátní sítě pro DB a frontend).
      • Kontinuální skenování: Používat statické skenery (SAST) a dynamické analýzy kontejnerových
        image (např. Trivy, Clair) ke zjištění známých zranitelností v závislostech či operačním systému.
        Pravidelně provádět penetrační testy či audit bezpečnosti.
                                                       1
   • Resilience vzory: Implementovat bezpečnostní a odolnostní vzory jako circuit breaker,
     bulkhead (izolaci částí systému) či service mesh (Istio/Linkerd) pro automatické provádění mTLS
     mezi službami. Tím se zabrání domino efektu při selhání nebo útoku na jednu mikroslužbu.
   • Bezpečnost RAG a vektorových DB: V případě ukládání citlivých dat ve vektorové DB (pgvector)
     zajistit přísné RBAC – odmítat ukládání citlivých informací, pokud DB nepodporuje detailem řízení
     přístupů. Preferovat systémy s granulární autentizací (např. Qdrant s claims) a integrovat je s
     IAM/PAM řešeními.
Správa tajemství (Secrets)
   • Dedikovaný secrets manager: Není vhodné ukládat klíče a hesla přímo v kódu či
     kontejnerovém image. Doporučuje se nasadit nástroj jako HashiCorp Vault, AWS Secrets
     Manager nebo Azure Key Vault, odkud si mikroslužby budou dynamicky vyzvedávat tajemství.
     Tyto systémy umožňují audit přístupu, automatickou rotaci a šifrování.
   • Kubernetes secrets: Pokud se používá Kubernetes, využít jeho Secret objekty s šifrováním na
     disk (KMS). Pro větší bezpečnost lze rozšířit Kubernetes o řešení jako Sealed Secrets nebo
     External Secrets (které integruje Vault) pro automatickou injektáž tajemství do podů.
   • Sidecar pattern pro načítání tajemství: Zvážit architektonický vzor, kdy ke každé službě poběží
     vedle „sidecar“ kontejner (např. Vault Agent), který se stará o získání a obnovení tajemství. Hlavní
     aplikace je pak nepotřebuje řešit přímo a získává je pouze lokálně.
   • Rotace tajemství: Plánovat pravidelnou výměnu (rotaci) klíčů, tokenů a certifikátů.
     Automatizovat rotaci na základě časového intervalu (např. každých 30–90 dní) nebo události.
     Důležitá je bezproblémová obnova (rolling update certifikátů bez vypnutí služby).
Škálovatelnost a výkon
   • Horizontální škálování: Využít automatické škálování podů/služeb (např. Kubernetes HPA/VPA,
     AWS/GCP autoscaler) dle zátěže. Definovat limity CPU, paměti a metriky (např. využití CPU, frontu
     zpráv) pro trigger auto-scaling. Zajistit robustní load balancer (nebo ingress) před
     mikroslužbami.
   • Kapacitní plánování a testy: Před nasazením definovat základní kapacitu (počet instancí,
     požadavky CPU/RAM) pro očekávanou zátěž. Provést zátěžové testy a validovat vertikální i
     horizontální škálování. Ověřit, že navyšování počtu replik či zdrojů udržuje výkon bez velkých
     výkonnostních ztrát.
   • Caching: Pro zrychlení odpovědí a odlehčení DB implementovat cache vrstvy. Například Redis
     nebo Memcached pro caching často čtených dotazů či vygenerovaných embeddingů. Na úrovni
     API Gateway či reverse proxy lze také cachovat odpovědi na REST volání (podobně jako AWS API
     Gateway Cache).
   • Optimalizace databáze: Optimalizovat PostgreSQL (indexy, partitioning, read-repliky). Uvažovat
     o sharding pro horizontální škálování (rozložení dat mezi více instancí). Pokud nároky rostou, lze
     zvažovat NoSQL/vektorové DB pro určité use-casy (Mongo, Cassandra, nebo specializované
     vektorové úložiště) pro lepší škálovatelnost a výkon.
   • Asynchronní komunikace: Použít message brokery pro oddělení komponent a zlepšení odezvy.
     Např. RabbitMQ, Kafka nebo cloudové služby (AWS SQS/Kinesis) umožní zpracovávat úlohy
     asynchronně, nezablokují uživatelské toky a zvýší propustnost systému. Asynchronní fronty také
     pomáhají přetlačit špičky a dovolí nezávislé škálování producentů a konzumentů.
   • Velikost mikroslužeb (granularita): Dbát na vyváženou granularitu – služby by nebyly extrémně
     drobné, aby se zbytečně nesnášela režie síťové komunikace. Vyznačit boundary podle business
     domén a výkonových charakteristik a ponechat logiku spíše kohezivní, ale ne příliš monolitickou.
                                                    2
CI/CD a DevOps procesy
    • Automatizovaná pipeline: Vytvořit komplexní CI/CD proces, který při každém „pushi“ do
      repozitáře spustí build, testy a nasazení. Může jít o GitHub Actions, GitLab CI/CD, Jenkins, nebo
      cloudové nástroje (Azure DevOps, CircleCI) spojené s GitOps nástroji jako ArgoCD nebo Flux pro
      Kubernetes. Pipeline by měla pokrývat kompilaci, kontejnerizaci (Docker image), unit/integration
      testy a nakonec nasazení na staging/prod.
    • Správa větví a releasů: Dodržovat čistou práci s gitem – používat feature větve, pull requesty a
      code review. Po schválení automaticky sestavovat a nasazovat. Pravidelně čistit staré větve a
      slučovat změny v chronologickém pořadí, aby nedocházelo ke konfliktům při mergování.
    • Bezpečnost pipeline: Nepoužívat veřejné CI služby bez vhodného zabezpečení – zajistit 2FA pro
      přístup k repozitáři a CI serveru. Uchovávat tajné klíče a tokeny pouze v secured storage (vault) a
      nepředávat je přímo v CI proměnných. Provádět pravidelně bezpečnostní audit CI/CD
      (Dependency Scanning, Secret Scanning) a monitorovat integritu pipeline.
    • Samoobnova a rollback: Konfigurovat orchestrátor (Kubernetes) pro health-checky a
      automatické restartování nefunkčních podů. Implementovat možnost rolování zpět (rollback) na
      starší verze kontejnerů při neúspěšném nasazení. Uvažovat o postupném nasazení (canary
      deployments) pro snížení rizika.
    • Infrastructure as Code: Veškerou infrastrukturu (sítě, k8s cluster, DB, CI/CD konfiguraci)
      definovat v kódu (Terraform, Pulumi, Ansible). Tím se zajistí opakovatelnost a audit změn. Helm
      Charts lze použít pro správu Kubernetes zdrojů.
Testování
    • Jednotkové testy (Unit): Pokrýt logiku každé mikroslužby a knihovny pomocí unit testů (pytest,
      unittest pro Python). Testovat co nejvíce edge-case scénářů a chování funkcí izolovaně.
      Implementovat i testy pro použité modulárních komponenty (např. servisní vrstvy, utility).
    • Integrační testy (Integration): Ověřovat společnou funkčnost jednotlivých služeb s reálnými
      zdroji (testovací Postgres, Elasticsearch apod.) či pomocí testovacích databází. Integrace s
      externími API nebo službami (např. embedding service, cloudové API) by měly být prozkoumány
      pomocí mocků nebo staging verze.
    • Kontraktové testy (Contract): Zajistit, aby rozhraní mikroslužeb odpovídala domluveným
      kontraktům (schema vstup/výstup). Např. s nástroji jako Pact lze psát testy, které ověří, že změna
      v API jedné služby neporuší očekávání ostatních. Tento přístup podporuje nezávislý vývoj a
      uvolnění mikroslužeb.
    • End-to-end testy (E2E): Simulovat kompletní uživatelské scénáře přes vícero služeb (např. od
      HTTP API až po front-end). Lze použít nástroje jako Selenium, Cypress (pro GUI) nebo Postman/
      Newman (pro REST API). Tyto testy by měly běžet periodicky nebo při větších nasazeních.
    • Testování modelů a dat: Provádět validační testy kvality modelů (měřit přesnost, F1-skóre
      apod.) na odděleném validačním datasetu. Zkontrolovat, že LoRA moduly generují očekávané
      výstupy. Použít i regresní testy modelu (porovnat chování nového modelu s předchozí verzí).
      Data-Quality testy (např. Great Expectations) mohou ověřovat, že nově ingestovaná data
      odpovídají očekávaným schématům a rozsahům.
    • Automatizace testů v CI/CD: Všechny uvedené testy zařadit do CI pipeline – unit a integrační při
      každém build, E2E např. po nasazení do testovacího prostředí. Díky tomu se minimalizuje riziko
      nasazení chybného kódu. Pravidelné spouštění testů také pomáhá odhalit únik citlivých
      informací (secret scanning) nebo dalším bezpečnostním slabinám.
                                                    3
Observabilita (monitoring, logování)
   • Logování: Implementovat centralizované logování ze všech mikroslužeb. Např. použít ELK stack
     (Elasticsearch, Logstash, Kibana) nebo Graylog/Fluentd. Sběrač logů (sidecar nebo DaemonSet)
     posbírá JSON/logy a předá je do společného úložiště. Udržovat jednotný formát logů (časová
     razítka, úrovně, trace ID) pro snadné vyhledávání a korelaci.
   • Distribuované trasování (Tracing): Použít OpenTelemetry, Jaeger nebo Zipkin pro sledování
     toku požadavků napříč službami. Každý HTTP request obohatit o unikátní identifikátor (trace ID),
     který se předává mezi volanými službami. Tracing umožní zjistit, kde vznikají úzká místa (latence)
     nebo chybové stavy v celém řetězci zpracování.
   • Metriky a metrické servery: Rozšířit Prometheus (již plánováno) o sběr klíčových metrik – doba
     odezvy, počet požadavků, chybovost, využití paměti/CPU atd. Nastavit exportéry pro DB (node-
     exporter, postgres-exporter) a vlastní business metriky. Na základě metrik definovat SLO/SLI
     (např. 99 % requestů do 200 ms). S využitím Prometheus Alertmanageru vytvořit upozornění na
     překročení prahů (chyby, latency spikes).
   • Dashboardy a reporting: V Grafaně vytvořit přehledné dashboardy pro provozní metriky (stav
     clusteru, služby) i bizmetriky (počet interakcí, úspěšnost predikcí). Zahrnout i vizualizaci MLOps
     metrik, např. vyhodnocení kvality modelu v čase.
   • Health-checky a heartbeaty: Každá služba by měla mít endpoint pro kontrolu zdraví (např. /
     health ), který reportuje základní stav (dostupnost DB, konektivitu ke klíčovým službám).
     Orchestrátor (Kubernetes) tyto endpointy využije pro restart či odstavení nefunkčních instancí.
Udržitelnost a rozšiřitelnost (Maintenance & Extensibility)
   • Kvalita kódu a dohledatelnost: Používat lintery (flake8, pylint) a formátovací nástroje (black,
     isort) pro jednotný styl kódu. Zavést code review pravidla a měřit pokrytí testy. Static code
     analysis (např. SonarQube, Snyk) pomůže odhalit problémy dříve, než je nasadíme.
   • Dokumentace: Pro každou službu vytvořit podrobný API spec (OpenAPI/Swagger) a hostovat jej
     (ve vývojovém portálu nebo automaticky ve Swagger UI). Dále vést Architectural Decision
     Records (ADR) pro klíčová rozhodnutí. Dokumentovat architekturu systému, datové modely i
     postupy nasazení ( README , Contributing guide).
   • Verzování a kompatibilita: Používat semver (verzování) pro služby a API. Při změnách v
     kontraktech (API) zajistit podporu backward compatibility, nebo jasně definovat migrační cesty.
     Testovat interoperabilitu nových verzí služeb se staršími klienty.
   • Infrastructure as Code: Infrastrukturu (sítě, DB, K8s konfiguraci) spravovat deklarativně
     (Terraform, Ansible, CloudFormation). Tím se sníží manuální zásahy a zajistí konzistentní
     prostředí např. pro vývoj i produkci.
   • Šablony a generator kódu: Pro mikroslužby využít standardizované šablony (boilerplate), aby
     nové služby měly jednotnou strukturu (stejné foldery, prostředí, CI scripts). Může to být např.
     GitHub template repo nebo návrh v Cookiecutter.
   • Feature toggles: Při vývoji nových funkcí používat feature flags pro postupné zapínání do
     produkce bez nutnosti redeploy (např. LaunchDarkly, Flagsmith). To zvýší flexibilitu a umožní A/B
     testování.
   • Data backup a obnovení: Definovat plán zálohování DB (point-in-time backup, replikace,
     geografické zálohy) a procedury disaster recovery. Pravidelně testovat obnovu dat. Udržovat
     alespoň jednu replikaci databáze pro vysokou dostupnost.
   • Licence a audit: Mít jasno v licencích použitých knihoven (open-source compliance) a auditovat
     složky kódu na citlivé informace (GDPR atd.).
                                                    4
AI/MLOps specifika
     • Experiment tracking: Pro LoRA tréninky a ladění modelů zavést systém sledování experimentů
       (např. MLflow, Weights&Biases, nebo Neptune.ai). Umožní uchovávat verze dat,
       hyperparametrů a výsledky tréninků. Pomůže tak reprodukovat a porovnávat výstupy modelů.
     • Model registry: Implementovat centrální registry modelů, kde se budou spravovat verze modelů
       (včetně LoRA adaptací). To usnadní nasazování, audit a rollback modelů. Příklady: MLflow Model
       Registry, DVC, nebo specializované služby (AWS SageMaker Model Registry).
     • CI/CD pro modely (CT – Continuous Training): Zařadit do DevOps workflow také tréninkový
       pipeline. Např. při změně tréninkových dat nebo parametrů automaticky spustit trénink nového
       modelu a výsledky nasadit, pokud splňují předem definované metriky. Tím se zajistí udržování
       modelu čerstvého.
     • Data validation a drift monitoring: Pravidelně kontrolovat, zda nově příchozí data odpovídají
       distribučním předpokladům tréninku (Data Drift) a zda se model nerozbíjí novými hodnotami
       (Model Drift). Nastavit monitoring predikcí – pokud kvalita klesne, upozornit tým. Nástroje: Great
       Expectations pro datová pravidla, Evidently.ai pro drift analyzu.
     • Feature Store: Uvažovat o centralizované správě feature (např. Feast), zejména pokud více
       modelů používá společné transformace. Sjednocení pipeline umožní opětovné použití a
       konzistenci.
     • Bezpečnost modelu: Řešit potenciální nechtěné chování LLM (škodlivé výstupy). Mít mezi
       vrstvami filtrování promptů a odpovědí (zabraňovat injektování nebezpečného obsahu).
       Implementovat logování dotazů a odpovědí pro audit.
     • GPU vs CPU: Pokud trénink LoRA vyžaduje GPU, plánovat škálování GPU klastrů (např. GKE/AKS
       GPU node pool, Kubernetes Device Plugins).
     • Kontinuita provozu AI služby: Zajistit, že modelové služby (LLM inference) mají horizontální
       repliky a fallback (záloha), pokud dojde k přetížení či selhání (např. rozložení zátěže mezi více
       instancí modelu).
Návrh doporučených technologií a nástrojů
 Oblast                Doporučené nástroje / technologie
                       HashiCorp Vault (secret mgmt), Trivy / Clair (securitní skener), <br>Istio/Linkerd
 Bezpečnost            (mTLS service mesh), Kong / Apigee (API Gateway), <br>OAuth2/JWT knihovny
                       (FastAPI), SonarQube / Snyk (static analysis)
                       GitHub Actions / GitLab CI / Jenkins, ArgoCD / Flux (GitOps) / Helm
 CI/CD & Infra
                       <br>Terraform / Ansible / Pulumi (IAC), Docker Registry (Harbor)
                       Prometheus + Grafana (metriky), Jaeger / OpenTelemetry (tracing) <br>ELK
 Observabilita
                       Stack / Graylog (log aggregation), Loki/Grafana (centr. logy)
                       Kubernetes (HPA/VPA, pod autoscaling), Redis / Memcached (caching),
 Škálovatelnost        <br>Kafka / RabbitMQ / SQS (messaging), <br>AWS/GCP autoscaling, API
                       Gateway caching
                       pytest / unittest (unit tests), pytest-django/SQLAlchemy (integration), <br>Pact
 Testování             (contract tests), Selenium / Cypress / Playwright (E2E), <br>Great Expectations
                       (data validation)
                                                        5
  Oblast             Doporučené nástroje / technologie
                     MLflow / DVC / Weights&Biases (experiment tracking), <br>MLflow Model
                     Registry / DVC (model registry), <br>Kubeflow / Airflow / Prefect (pipeline
  MLOps / AI
                     orchestration), <br>Evidently.ai (drift monitoring), <br>Flake8 / Bandit (code
                     security), W&B (monitoring)
  Správa             HashiCorp Vault / AWS Secrets Manager, Kubernetes Secrets + SealedSecrets,
  tajemství          <br>AWS KMS / Azure Key Vault, Bitnami External Secrets
Každá oblast výše čerpá z osvědčených postupů a nástrojů doporučených v literatuře či průmyslu.
Uvedené technologické návrhy jsou příklady – konkrétní volba závisí na požadavcích, prostředí a
preferencích týmu. S implementací těchto doporučení se zvýší bezpečnost, spolehlivost a
udržovatelnost systému NESTOR.
Shrnutí: Doplněním zmíněných opatření a nástrojů (víceúrovňová ochrana, centralizovaná správa
tajemství, plně automatizované CI/CD, komplexní testování, observabilita 3 pilířů, MLOps procesy) bude
architektura robustnější, bezpečnější a lépe připravená na produkční provoz i budoucí rozšiřování.
                                                    6

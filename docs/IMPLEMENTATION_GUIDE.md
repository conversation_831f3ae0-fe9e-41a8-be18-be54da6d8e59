# Průvodce implementací projektu NESTOR

Tento dokument navazuje na hlavní plán projektu NESTOR (popsaný v `README.md`) a poskytuje detailnější ná<PERSON>hy, doporučení a strategie pro implementaci jednotlivých fází a řízení změn.

## Obecná strategie implementace

Navrhuji následující přístup, který doplňuje a konkretizuje principy uvedené v README:

1.  **Iterativní vývoj s tenkými řezy (Thin Slices):**
    *   Místo snahy o kompletní dokončení každé fáze před přechodem na další, zaměřte se na co nejrychlejší vytvoření funkčního "tenkého řezu" celým systémem. Například:
        *   <PERSON><PERSON><PERSON><PERSON><PERSON> `FastAPI` endpoint (`/ask`).
        *   Minimální `RAG Service`, který umí načíst jeden dokument a odpovědět na dotaz.
        *   Základní `LLM Service` s jedním modelem.
        *   `PostgreSQL` s `pgvector` pro uložení embeddingu tohoto jednoho dokumentu.
        *   Jednoduchý `CLI` klient pro otestování.
    *   Tento přístup rychle odhalí integrační problémy a umožní validovat architekturu v rané fázi. Poté můžete postupně rozšiřovat funkcionalitu jednotlivých komponent.

2.  **Automatizace od samého počátku (CI/CD):**
    *   Jakmile máte základní strukturu projektu (Fáze 1.1) a Docker setup (Fáze 1.2), okamžitě nastavte alespoň základní CI/CD pipeline (např. pomocí GitHub Actions, GitLab CI).
    *   Pipeline by měla minimálně zahrnovat: build Docker obrazů, spuštění testů, linting.
    *   To zajistí konzistenci a rychlou zpětnou vazbu.

3.  **Pravidelné revize a adaptace:**
    *   Na konci každého významnějšího milníku (např. dokončení klíčové části fáze nebo implementace "tenkého řezu") proveďte revizi.
    *   Zhodnoťte, co funguje, co ne, a zda je potřeba plán upravit. Buďte připraveni plán adaptovat na základě získaných zkušeností – to je podstata agilního přístupu.

## Postup implementace jednotlivých fází (s důrazem na "jak")

Níže jsou návrhy k realizaci jednotlivých fází z `README.md`:

### Fáze 1: Infrastruktura a kontejnerizace

*   **Priorita:** Toto je absolutní základ.
    *   **Kořenový adresář a podadresáře:** Vytvořte strukturu dle plánu.
    *   **`.gitignore`, `requirements.txt`:** Inicializujte je hned. Pro `requirements.txt` zvažte použití nástroje jako `pip-tools` (příkazy `pip-compile` a `pip-sync`) pro lepší správu závislostí a reprodukovatelné buildy.
    *   **Dockerfile templaty:** Vytvořte generický Dockerfile pro Python služby, který pak budete kopírovat a upravovat. Měl by zahrnovat best practices (multi-stage builds, non-root user).
    *   **`podman-compose.yml`:** Začněte s nejnutnějšími službami (např. API, databáze) a postupně přidávejte další. Používejte `depends_on` pro řízení startovací sekvence.
    *   **`.env` soubory:** Jasně definujte strukturu a poskytněte `.env.example` soubory pro každou službu.
    *   **Automatizace (Makefile/Justfile):** Implementujte základní cíle (`build`, `up`, `down`, `logs`, `test`) co nejdříve. Rozšiřujte je s přidáváním nových služeb a úkolů.
    *   **Skripty (`init.sh`, `download_models.sh`, `prepare_data.sh`):** Začněte s `init.sh` pro základní nastavení. Ostatní skripty vyvíjejte, až budou potřeba pro konkrétní fáze.

### Fáze 2: Databázová vrstva - PostgreSQL s pgvector

*   **Image a konfigurace:** Ověřte funkčnost `pgvector/pgvector:latest` a správné nastavení perzistence dat.
*   **Alembic pro migrace:** Nastavte Alembic hned na začátku. První migrace by měla vytvořit základní tabulky (`embeddings`, `documents`, `metadata`). Pečlivě navrhněte sloupce a jejich typy, zejména pro indexaci vektorů.
*   **Data governance:** Zvažte, jak budete spravovat verze schématu a testovací data. Pro verzování datasetů a paměti (jak je zmíněno) můžete začít jednoduchým konvencemi v názvech souborů/složek a později zvážit DVC (Data Version Control) nebo podobné nástroje.

### Fáze 3: LLM Service

*   **Výběr modelu:** Pro začátek vyberte jeden menší, dobře podporovaný model pro `llama.cpp`, abyste rychle ověřili funkčnost služby.
*   **Mount modelů:** Důkladně otestujte mountování modelů z hosta. Zajistěte, aby cesty byly konfigurovatelné přes `.env`.
*   **`llm_interface/client.py`:** Tento wrapper by měl být navržen tak, aby byl snadno použitelný z ostatních služeb (např. RAG Service, API Backend) a aby abstrahoval detaily komunikace s `llama.cpp` serverem. Implementujte robustní error handling a retry mechanismy.

### Fáze 4: RAG Service

*   **Proof of Concept (PoC):** Než se pustíte do plné implementace, vytvořte malý PoC:
    1.  Načtení jednoho textového souboru.
    2.  Vygenerování embeddingu (např. pomocí `sentence-transformers` z LangChain).
    3.  Uložení textu a embeddingu do PostgreSQL/pgvector.
    4.  Provedení kNN vyhledávání na základě dotazu.
    5.  Sestavení promptu s načteným kontextem a dotazem.
    6.  Odeslání promptu do LLM Service a získání odpovědi.
*   **Moduly (`indexer.py`, `embedder.py`, `retriever.py`, `pipeline.py`):** Tato struktura je dobrá. Začněte s `embedder.py` a `retriever.py` pro PoC.
*   **LangChain + `langchain-postgres`:** Prozkoumejte možnosti a omezení této kombinace.
*   **Fallback strategie:** Implementujte jednoduchý fallback (např. "Nerozumím" nebo předání dotazu přímo LLM bez kontextu) a postupně ho vylepšujte.

### Fáze 5: MCP (Memory Context Processor)

*   **Formát dat:** JSON-L je dobrá volba pro streamování a jednoduché parsování. Definujte přesné schéma pro různé typy paměti.
*   **API Endpoints:** Začněte s `add` a `get` endpointy. `merge` a `export` mohou přijít později.
*   **Integrace s RAG:** Přemýšlejte, jak bude MCP interagovat s RAG. Budou vzpomínky také vektorizovány a prohledávány?

### Fáze 6: API Backend (FastAPI)

*   **Orchestrace:** API bude hlavním bodem orchestrace. Postupně přidávejte endpointy, jakmile budou dostupné podkladové služby (LLM, RAG, MCP).
*   **OpenTelemetry:** Integrujte OpenTelemetry co nejdříve, alespoň pro základní tracing požadavků mezi službami. To pomůže při ladění a monitorování.

### Fáze 7: Frontend (Vue.js + Nginx)

*   **Mock server:** Využijte mock server (např. `json-server` nebo vlastní mocky ve Vue) pro vývoj UI komponent nezávisle na backendu.
*   **Komponenty:** Začněte s klíčovými komponentami jako `ChatWindow`.
*   **UX:** Onboarding modul je skvělý nápad. Zapojte potenciální uživatele (nebo alespoň tým) do testování UX co nejdříve.

### Fáze 8: Training & LoRA

*   **Příprava dat:** Toto je často nejnáročnější část. Vytvořte robustní skripty pro čištění a formátování dat.
*   **Experimentace:** Trénink LoRA je experimentální. Počítejte s časem na ladění hyperparametrů a vyhodnocování výsledků.
*   **Logging (`wandb`/`tensorboard`):** Integrujte od začátku pro sledování tréninkových metrik.

### Fáze 9: CLI + testy

*   **CLI (`nestor_cli.py`):** Vyvíjejte CLI paralelně. Může sloužit jako mocný nástroj pro testování jednotlivých služeb a pro automatizaci úkolů.
*   **Testy (`pytest`, `pytest-cov`, `tox`):**
    *   Pište unit testy pro jednotlivé moduly a funkce.
    *   Pište integrační testy pro interakce mezi službami (např. API volá RAG, RAG volá LLM). Tyto testy je nejlepší spouštět v rámci `podman-compose` prostředí.
    *   `tox` je skvělý pro testování v různých prostředích/verzích Pythonu.

### Fáze 10: Dokumentace a nasazení

*   **Živá dokumentace:** `README.md` by mělo být neustále aktualizováno. API dokumentaci generujte automaticky z kódu FastAPI (Swagger/OpenAPI).
*   **Deployment:**
    *   **Lokální:** Ujistěte se, že `podman-compose.yml` je dostatečný pro lokální vývoj a testování.
    *   **Cloud-ready:** Přemýšlejte o konfiguraci pro cloud (proměnné prostředí, správa secretů, škálovatelnost).
*   **Security:** I když je autentizace a autorizace plánována na později, dodržujte bezpečnostní principy od začátku (validace vstupů, minimalizace oprávnění, atd.).

## Řízení změn v plánu

Plán je skvělý, ale realita vývoje softwaru často přináší překvapení. Jak na změny:

1.  **Transparentnost:** Každý návrh na změnu oproti původnímu plánu by měl být diskutován v týmu.
2.  **Chain-of-Thought (CoT):** Stejně jako u původních rozhodnutí, i u změn dokumentujte důvody. Proč je změna nutná? Jaký má dopad?
3.  **Impact Analysis:** Zvažte dopad změny na ostatní komponenty, časový harmonogram a zdroje.
4.  **Verzování plánu:** Pokud dojde k větším změnám, zvažte "verzi" plánu, aby bylo jasné, podle čeho se postupuje. `CHANGELOG.md` může být užitečný i pro sledování vývoje samotného plánu.
5.  **Flexibilita vs. Scope Creep:** Buďte flexibilní, ale zároveň hlídejte, aby se projekt nerozrostl nad rámec původního záměru bez jasného zdůvodnění (scope creep).

## Klíčové metriky a monitorování

Princip "Monitorování a Observabilita" je klíčový.
*   **Definujte metriky brzy:** Pro každou službu (LLM, RAG, API) definujte, co chcete měřit (počet tokenů, latence, hit rate, chybovost) a jak.
*   **Integrace s Prometheus/Grafana:** Začněte s integrací monitoringu postupně, jakmile jsou služby funkční, nečekejte na konec. Každá služba by měla vystavovat metriky ve formátu, který Prometheus dokáže sbírat.

Tento detailní plán je výborným startem. S iterativním přístupem, důrazem na automatizaci a průběžným monitorováním máte velkou šanci projekt NESTOR úspěšně dotáhnout do konce.

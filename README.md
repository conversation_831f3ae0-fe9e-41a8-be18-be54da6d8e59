# **Kompletní plán projektu NESTOR (aktualizovaná verze)**

Tento dokument detailně popisuje plán pro vývoj projektu NESTOR, s důrazem na kontejnerizaci, architekturu mikroslužeb a použití PostgreSQL s rozšířením pgvector jako primární databáze pro ukládání vektorů. Verze obsahuje vylepšení a rozšíření navržené v reakci na zpětnou vazbu a nejlepší praktiky z oblasti vývoje AI systémů.

## **Principy**

* **Detailní plánování před implementací:** Každý krok bude podrobně pop<PERSON>án, včetně jeho účelu, výběru technologie a dopadu na ostatní komponenty.  
* **Krok za krokem:** Budeme se striktně držet posloupnosti. Implementace začne a<PERSON>, co bude plán dostatečně detailní.  
* **Kontejnerizace od počátku:** Všechny služby budou od začátku navrženy s ohledem na kontejnerizaci.  
* **Automatizace:** Každý krok bude zvažovat možnosti automatizace (Makefiles, skripty) pro snadné nasazení a správu.  
* **CoT (Chain-of-Thought):** Budeme explicitně uvádět důvody pro každé rozhodnutí a vysvětlovat souvislosti.  
* **Monitorování a Observabilita:** Systém bude mít metriky, tracing a health-checky pro lepší správu a debugování. **Budeme měřit klíčové metriky jako počet tokenů zpracovaných LLM, latenci inference, úspěšnost (hit rate) retrieveru v RAG, a celkovou dobu odezvy API.**

## **Architektonická vizualizace**

                 +-------------------------------+  
                 |      Frontend (Vue.js)        |  
                 |   (kontejnerizovaný Nginx)    |  
                 +-------------------------------+  
                               |  
                               v  
                 +----------------------------+  
                 |      REST API (FastAPI)    |  
                 |        (kontejner)         |  
                 +----------------------------+  
                               |  
       +---------+-----------+-----------+----------+  
       |                     |           |          |  
       v                     v           v          v  
+------------------+  +------------------+ +------------------+ +------------------+  
| LLM Service      |  | PostgreSQL DB    | | Memory Core (MCP)| | RAG Service      |  
|(llama.cpp)       |  | (pgvector)       | | (kontejner)      | | (kontejner)      |  
|(kontejner)       |  | (kontejner)      | +------------------+ +------------------+  
+------------------+  +------------------+  
                               |  
                               v  
                         +----------------------+  
                         | Training & LoRA      |  
                         | (utility kontejnery) |  
                         +----------------------+  
                               |  
                               v  
                         +----------------------+  
                         | Monitoring Service   |  
                         | (Prometheus, Grafana)|  
                         +---------------------+

## **Fáze 1: Infrastruktura a kontejnerizace**

**Cíl:** Mít plně funkční, kontejnerizovanou základní infrastrukturu, připravenou pro vývoj jednotlivých služeb.

1.1 Projektová struktura

* Kořenový adresář nestor/  
* Podadresáře: api/, llm\_interface/, memory/, lora/, vectorstore/, data/, frontend/, cli/, utils/, tests/, setup/, models/, zip/  
* Init: README.md, .gitignore, requirements.txt, CHANGELOG.md

1.2 Docker a Compose

* Template Dockerfile pro Python-based služby  
* podman-compose.yml s depends\_on  
* .env soubory pro každou službu

1.3 Automatizace

* Makefile nebo Justfile pro: init, build, up, down, test, download-models  
* Setup skripty: init.sh, download\_models.sh, prepare\_data.sh

## **Fáze 2: Databázová vrstva \- PostgreSQL s pgvector**

**Cíl:** Spustit databázi v kontejneru a umožnit práci s embeddingy.

2.1 Image a konfigurace

* Použít pgvector/pgvector:latest  
* Perzistence přes volume pgdata  
* Environment proměnné pro DB přístup  
* Možnosti rotace hesel (do budoucna Vault/SOPS)

2.2 Inicializace a migrace

* Schéma přes Alembic  
* Tabulky: embeddings, documents, metadata  
* **Data governance:** Zvážení verzování dat (např. pomocí názvů složek nebo Git-like metadat pro datasety a paměť).

## **Fáze 3: LLM Service**

**Cíl:** Kontejnerizovaná služba pro inferenci modelu přes llama.cpp

* Využít předpřipravený image (CPU/GPU)  
* Mount modelů z hosta (nikoliv build-time)  
* Health-check endpoint  
* llm\_interface/client.py: API wrapper

## **Fáze 4: RAG Service**

**Cíl:** Vlastní služba pro retrieval, embedding a dotazování nad PostgreSQL

* LangChain \+ langchain-postgres  
* Moduly: indexer.py, embedder.py, retriever.py, pipeline.py  
* Fallback strategie, pokud není nalezen dokument  
* **RAG optimalizace:** Zahrnout experimentální fázi pro testování různých strategií retrievalu (kNN vs hybridní, různé embedding modely).

## **Fáze 5: MCP (Memory Context Processor)**

**Cíl:** Dlouhodobá paměť, kontexty, práce se vzpomínkami

* JSON-L nebo jiný strukturovaný formát  
* Typ paměti (osoba, vztah, myšlenka, událost)  
* TTL \+ export/import profilů  
* Endpointy: add, get, merge, export  
* **Data governance:** Zvážení verzování modelových checkpointů a paměti (např. pomocí názvů složek nebo Git-like metadat).

## **Fáze 6: API Backend (FastAPI)**

**Cíl:** Centrální orchestrátor pro frontend, CLI a agenty

* Endpointy: /ask, /memory, /simulate  
* Rozšíření /simulate na start, stop, status  
* Middleware pro tracing (OpenTelemetry)

## **Fáze 7: Frontend (Vue.js \+ Nginx)**

**Cíl:** Webové rozhraní pro uživatele

* Komponenty: ChatWindow, MemoryCard, PersonaSelector  
* Mock server pro vývoj bez backendu  
* **UX:** Zvážit přidání onboardingového modulu, který uživatele provede simulací od začátku (např. „Vyberte si osobnost, zadejte její kontext, spusťte konverzaci“).

## **Fáze 8: Training & LoRA**

**Cíl:** Příprava dat, skriptů a LoRA trénink

* lora/data/, lora/scripts/  
* Tréninkové CLI s argumenty: \--model, \--data, \--steps  
* Logging přes wandb nebo tensorboard

## **Fáze 9: CLI \+ testy**

**Cíl:** Přímá interakce a zajištění kvality

* CLI (nestor\_cli.py): ask, simulate, add-memory, formátování (json/plain)  
* pytest, pytest-cov, tox

## **Fáze 10: Dokumentace a nasazení**

**Cíl:** Kompletní instrukce pro vývoj, provoz i deployment

* README.md, API docs, servisní konfigurace  
* Nasazení: lokální (Podman Compose), cloud-ready (OCI kompatibilní)  
* Migrace osobností, záloh LoRA, podpora GGUF  
* **Security:** Přidat poznámku k budoucí implementaci autentizace a autorizace (např. OAuth2 pro API, role-based access pro MCP) – nyní není nutné, ale při škálování systému to bude důležité.

Tento plán je základem produkčně připraveného systému s důrazem na modularitu, udržitelnost a budoucí rozšířitelnost. Každá fáze může být samostatně vyvíjena, testována a nasazována, což umožní agilní vývoj a experimentaci s jednotlivými komponentami systému.

## **Průvodce implementací**

Pro detailní návrhy, doporučení k implementaci jednotlivých fází, osvědčené postupy a strategii řízení změn, nahlédněte do dokumentu [IMPLEMENTATION_GUIDE.md](./IMPLEMENTATION_GUIDE.md).
